package com.jidou.cariad.content.common.aspect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.jidou.cariad.content.common.annotation.MyCache;
import com.jidou.cariad.content.common.content.RedisContent;
import com.jidou.cariad.content.common.exception.exceptions.BizException;
import com.jidou.cariad.content.common.service.AsyncCacheRefreshService;
import com.jidou.cariad.content.common.utils.RedisUtil;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.StandardEnvironment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

/**
 * 缓存aspect
 *
 * <AUTHOR>
 * @since 2024/04/26 15:21
 */
@Aspect
@Component
public class CacheAspect {
    private static final String IGNORE_CACHE = "ignoreCache";
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private StandardEnvironment environment; // 确保您的类能够注入 Environment 对象
    @Resource
    private AsyncCacheRefreshService asyncCacheRefreshService;
    /**
     * 缓存即将过期的阈值（秒）
     */
    @Value("${cache.expire.soon.threshold:300}")
    private long cacheExpireSoonThreshold; // 5分钟

    /**
     * 缓存转换
     *
     * @param isList 是否是List类型
     * @param type   缓存类型
     * @param cache  缓存内容
     * @return java.lang.Object
     * <AUTHOR>
     * @since 2024/07/01 13:59
     */
    private static Object convertCacheAndReturn(boolean isList, Class<?> type, String cache) {
        if (isList) {
            return JSON.parseArray(cache, type);
        }
        // 如果是基本类型（String、Integer等），直接反序列化
        if (type == String.class || type.isPrimitive() || type == Integer.class || type == Long.class
                || type == Double.class || type == Boolean.class) {
            return JSON.parseObject(cache, type);
        }

        // 复杂对象类型的处理
        JSONObject jsonObject = JSON.parseObject(cache);
        if (StringUtils.isNotEmpty((String) jsonObject.get("@type"))) {
            throw new BizException((Integer) jsonObject.get("code"), (String) jsonObject.get("message"));
        }
        return JSON.parseObject(cache, type);
    }

    @Around("@annotation(com.jidou.cariad.content.common.annotation.MyCache)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Method method = getMethod(joinPoint);
        MyCache myCache = method.getAnnotation(MyCache.class);
        String key = generateKey(joinPoint, method, myCache);
        // 检查是否忽略缓存
        boolean ignoreCache = checkIgnoreCache(method, joinPoint);
        if (ignoreCache) {
            Object proceed = joinPoint.proceed();
            asyncCacheRefreshService.refreshCache(joinPoint.getTarget(), method, key, getExpireTime(myCache),
                                                  joinPoint.getArgs());
            return proceed;
        }

        boolean isList = false;

        Class<?> type = method.getReturnType();
        // 如果type是List类型，则获取泛型的类型
        if (type.isAssignableFrom(List.class)) {
            type = myCache.type();
            isList = true;
        }


        return processTheCache(joinPoint, key, isList, type, myCache);
    }

    /**
     * @param method
     * @return boolean
     * <AUTHOR>
     * @since 2024/08/10 10:09
     */
    private boolean checkIgnoreCache(Method method, ProceedingJoinPoint joinPoint) {
        Parameter[] parameters = method.getParameters();
        for (int i = 0; i < parameters.length; i++) {
            Parameter parameter = parameters[i];
            if (StringUtils.equals(parameter.getName(), IGNORE_CACHE)) {
                // 返回参数IGNORE_CACHE的boolean值
                return (boolean) joinPoint.getArgs()[i];
            }

        }
        return false;
    }

    /**
     * 缓存处理
     *
     * @param joinPoint 切面点
     * @param key       缓存key
     * @param isList    是否是List类型
     * @param type      缓存类型
     * @param myCache   注解
     * @return java.lang.Object
     * <AUTHOR>
     * @since 2024/07/01 13:57
     */

    private Object processTheCache(ProceedingJoinPoint joinPoint, String key, boolean isList, Class<?> type,
                                   MyCache myCache) throws Throwable {
        String cache = redisUtil.getString(key);
        if (StringUtils.isNotEmpty(cache)) {
            // 缓存命中，检查是否即将过期
            long expireTime = redisUtil.getExpire(key);
            if (expireTime > 0 && expireTime < cacheExpireSoonThreshold) {
                // 缓存即将过期，异步刷新缓存
                try {
                    Method method = getMethod(joinPoint);
                    asyncCacheRefreshService.refreshCache(joinPoint.getTarget(), method, key, getExpireTime(myCache),
                                                          joinPoint.getArgs());
                } catch (Exception e) {
                    // 异步刷新缓存失败，不影响当前请求
                }
            }
            // 直接代替方法返回
            return convertCacheAndReturn(isList, type, cache);
        }

        // 缓存未命中，执行方法
        Object result = joinPoint.proceed();
        if (!Objects.isNull(result)) {
            redisUtil.setString(key, JSON.toJSONString(result), getExpireTime(myCache));
        }
        return result;
    }

    /**
     * 获取缓存过期时间
     *
     * @param myCache MyCache注解
     * @return long
     * <AUTHOR>
     * @since 2024/07/01 13:54
     */
    private long getExpireTime(MyCache myCache) {
        long expire = myCache.expire();

        if (StringUtils.isNotBlank(myCache.expireTimeKey()) && StringUtils.isNotEmpty(
                environment.getProperty(myCache.expireTimeKey()))) {
            expire = Long.parseLong(Objects.requireNonNull(environment.getProperty(myCache.expireTimeKey())));
        }
        if (myCache.expireToday()) {
            expire = RedisContent.getTodayExpireTime();
        }
        if (myCache.expireEndMonth()) {
            expire = RedisContent.getEndOfMonthExpireTime();
        }
        return expire;
    }

    /**
     * 生成缓存key
     *
     * @param joinPoint 切面点
     * @param method    方法
     * @param myCache   MyCache注解
     * @return java.lang.String
     * <AUTHOR>
     * @since 2024/07/01 13:54
     */
    private String generateKey(JoinPoint joinPoint, Method method, MyCache myCache) {
        StringBuilder builder = new StringBuilder();
        String key = myCache.key();
        // 如果key为空，则使用方法名作为key
        if (StringUtils.isBlank(key)) {
            key = method.getName();
        }
        // 填充参数的值并用‘:’分隔
        Parameter[] parameters = method.getParameters();
        for (int i = 0; i < parameters.length; i++) {
            // 忽略ignoreCache参数
            if (!StringUtils.equals(IGNORE_CACHE, parameters[i].getName())) {
                builder.append(":").append(joinPoint.getArgs()[i]);
            }
        }
        if (myCache.expireHour()) {
            return key + builder + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH"));
        }
        return key + builder;
    }

    /**
     * 获取方法
     *
     * @param joinPoint 切面点
     * @return java.lang.reflect.Method
     * <AUTHOR>
     * @since 2024/07/01 13:55
     */
    private Method getMethod(JoinPoint joinPoint) throws NoSuchMethodException {
        // 获取被调用的方法的所有参数
        Class<?> targetClass = joinPoint.getTarget().getClass();
        String methodName = joinPoint.getSignature().getName();
        Class<?>[] parameterTypes = new Class<?>[joinPoint.getArgs().length];
        for (int i = 0; i < joinPoint.getArgs().length; i++) {
            if (ObjectUtils.isEmpty(joinPoint.getArgs()[i])) {
                parameterTypes[i] = String.class;
                continue;
            }
            parameterTypes[i] = joinPoint.getArgs()[i].getClass();
        }
        return targetClass.getMethod(methodName, parameterTypes);
    }
}
