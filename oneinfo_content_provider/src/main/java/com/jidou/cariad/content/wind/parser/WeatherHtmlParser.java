package com.jidou.cariad.content.wind.parser;

// 1. Java核心包

import com.jidou.cariad.content.common.annotation.MyCache;
import com.jidou.cariad.content.common.content.RedisContent;
import com.jidou.cariad.content.wind.config.HttpClientFactory;
import com.jidou.cariad.content.wind.config.ProxyAwareHttpClient;
import com.jidou.cariad.content.wind.dto.WindData;
import com.jidou.cariad.content.wind.service.KuaidailiApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.util.EntityUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 气象HTML解析工具
 * 用于从气象网站页面解析天气数据
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WeatherHtmlParser {

    // 风速数据正则表达式匹配模式(例如: 13.8m/s)
    private static final Pattern WIND_SPEED_PATTERN = Pattern.compile("^\\d+(\\.\\d+)?m/s$");
    // 最大重试次数
    private static final int MAX_RETRY = 3;
    private final HttpClientFactory httpClientFactory;
    private final KuaidailiApiService kuaidailiApiService;

    /**
     * 获取指定地区的逐小时风速数据
     * 使用缓存优化，避免重复的HTTP请求和HTML解析
     * 缓存时间：6天（518400秒），与风速数据处理策略保持一致
     *
     * @param url 气象网站URL，例如："http://nmc.cn/publish/forecast/ASH/fengxian.html"
     * @return 风速数据列表，每个元素为WindData对象
     */
    @MyCache(key = RedisContent.WEATHER_WIND_URL_DATA_PREFIX, expire = 518400L, type = WindData.class)
    public List<WindData> getHourlyWindSpeed(String url, Boolean ignoreCache) {
        List<WindData> result = new ArrayList<>();

        try {
            String htmlContent = fetchHtmlContent(url);
            Document doc = Jsoup.parse(htmlContent);

            // 验证DOM结构
            validateDomStructure(doc, url);

            // 查找包含风速数据的元素
            Elements hourElements = doc.select("#hourValues .hour3");

            for (Element hourElement : hourElements) {
                // 获取时间 - 使用child(0)直接获取第一个子元素
                String time = "";
                Element timeElement = hourElement.child(0);
                if (timeElement != null) {
                    time = timeElement.text().trim();
                }

                // 获取风速 - 使用child(4)直接获取第五个子元素
                String windSpeed = "";
                Element windSpeedElement = hourElement.child(4);
                if (windSpeedElement != null) {
                    windSpeed = windSpeedElement.text().trim();
                    // 验证风速格式
                    if (!validateWindSpeedFormat(windSpeed)) {
                        log.warn("风速数据格式不正确: {}", windSpeed);
                    }
                }

                // 获取风向 - 使用child(5)直接获取第六个子元素
                String windDirection = "";
                Element windDirectionElement = hourElement.child(5);
                if (windDirectionElement != null) {
                    windDirection = windDirectionElement.text().trim();
                }

                // 创建WindData对象并添加到结果列表
                if (!time.isEmpty() || !windSpeed.isEmpty() || !windDirection.isEmpty()) {
                    WindData windData = WindData.builder()
                            .time(time)
                            .windSpeed(windSpeed)
                            .windDirection(windDirection)
                            .build();
                    result.add(windData);
                }
            }
        } catch (Exception e) {
            log.error("解析风速数据失败：{}", e.getMessage(), e);
            throw new RuntimeException("气象局页面结构变动: " + e.getMessage(), e);
        }

        return result;
    }

    /**
     * 验证DOM结构是否符合预期
     *
     * @param doc Jsoup Document对象
     * @param url 请求的URL
     */
    private void validateDomStructure(Document doc, String url) {
        // 检查主容器是否存在
        Elements mainContainer = doc.select("#hourValues");
        if (mainContainer.isEmpty()) {
            String message = "气象局页面结构变动: 未找到#hourValues容器";
            log.warn(message + ", URL: {}", url);
            throw new RuntimeException(message);
        }

        // 检查小时数据容器
        Elements hourElements = mainContainer.select(".hour3");
        if (hourElements.isEmpty()) {
            String message = "气象局页面结构变动: 未找到.hour3元素";
            log.warn(message + ", URL: {}", url);
            throw new RuntimeException(message);
        }

        // 检查数据项结构是否符合预期
        Element firstHour = hourElements.first();
        Elements divs = firstHour.select("div");
        if (divs.size() < 6) {
            String message = "气象局页面数据结构变动: 数据项数量不足";
            log.warn(message + ", URL: {}", url);
            throw new RuntimeException(message);
        }

        // 检查风速位置是否正确
        String windSpeedText = divs.get(4).text().trim();
        if (windSpeedText.isEmpty()) {
            String message = "气象局页面数据结构变动: 风速数据位置为空";
            log.warn(message + ", URL: {}", url);
            throw new RuntimeException(message);
        }
    }

    /**
     * 验证风速格式是否正确
     *
     * @param windSpeed 风速字符串
     * @return 是否符合格式
     */
    private boolean validateWindSpeedFormat(String windSpeed) {
        return WIND_SPEED_PATTERN.matcher(windSpeed).matches();
    }

    /**
     * 获取指定风速值
     *
     * @param url       气象网站URL
     * @param hourIndex 小时索引，从0开始
     * @return 风速字符串，如果无法获取则返回null
     */
    public String getWindSpeedAtIndex(String url, int hourIndex) {
        try {
            String htmlContent = fetchHtmlContent(url);
            Document doc = Jsoup.parse(htmlContent);

            // 验证DOM结构
            validateDomStructure(doc, url);

            Elements hourElements = doc.select("#hourValues .hour3");
            if (hourIndex >= 0 && hourIndex < hourElements.size()) {
                Element targetHour = hourElements.get(hourIndex);
                Elements windSpeedElements = targetHour.select("div:nth-child(5)");
                if (!windSpeedElements.isEmpty()) {
                    String windSpeed = windSpeedElements.first().text().trim();
                    // 验证风速格式
                    if (!validateWindSpeedFormat(windSpeed)) {
                        log.warn("风速数据格式不正确: {}", windSpeed);
                    }
                    return windSpeed;
                }
            }
        } catch (Exception e) {
            log.error("获取风速数据失败：{}", e.getMessage(), e);
            throw new RuntimeException("气象局页面结构变动: " + e.getMessage(), e);
        }

        return null;
    }

    /**
     * 获取最新的风速值
     *
     * @param url 气象网站URL
     * @return 最新的风速字符串，如果无法获取则返回null
     */
    public String getLatestWindSpeed(String url) {
        return getWindSpeedAtIndex(url, 0);
    }

    /**
     * 从URL获取HTML内容
     *
     * @param url 网页URL
     * @return HTML内容字符串
     * @throws IOException 请求失败时抛出异常
     */
    private String fetchHtmlContent(String url) throws IOException {
        int retryCount = 0;
        IOException lastException = null;

        while (retryCount < MAX_RETRY) {
            try (ProxyAwareHttpClient httpClient = httpClientFactory.createHttpClient()) {
                HttpGet httpGet = new HttpGet(url);
                httpGet.setHeader("User-Agent",
                                  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
                log.info("正在获取HTML内容: {}", url);
                try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
                    int statusCode = response.getStatusLine().getStatusCode();
                    if (statusCode != 200) {
                        throw new IOException("HTTP请求失败，状态码: " + statusCode);
                    }

                    HttpEntity entity = response.getEntity();
                    if (entity != null) {
                        return EntityUtils.toString(entity, "UTF-8");
                    }
                }

                // 如果执行到这里，说明未能获取到内容，但没有抛出异常
                throw new IOException("获取HTML内容失败，响应为空");
            } catch (IOException e) {
                lastException = e;
                log.warn("获取HTML内容失败(重试 {}/{}): {}", retryCount + 1, MAX_RETRY, e.getMessage());
                retryCount++;

                // 更新代理 ip
                tryChangeProxyIp();

                if (retryCount < MAX_RETRY) {
                    try {
                        // 重试前等待一段时间
                        Thread.sleep(1000 * retryCount);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new IOException("获取HTML内容被中断", ie);
                    }
                }
            }
        }

        // 所有重试都失败，抛出最后一个异常
        throw new IOException("获取HTML内容失败，重试" + MAX_RETRY + "次后仍然失败: " + (lastException != null
                ? lastException.getMessage() : "未知错误"), lastException);
    }

    /**
     * 尝试更换代理IP
     * 在HTTP请求失败时调用，用于更换快代理的IP地址
     */
    private void tryChangeProxyIp() {
        try {
            if (kuaidailiApiService.canChangeIp()) {
                log.info("正在尝试更换快代理IP...");
                boolean success = kuaidailiApiService.changeProxyIp();
                if (success) {
                    log.info("快代理IP更换成功");
                } else {
                    log.warn("快代理IP更换失败");
                }
            } else {
                log.debug("当前不满足更换代理IP的条件，跳过更换");
            }
        } catch (Exception e) {
            log.warn("更换代理IP时发生异常: {}", e.getMessage(), e);
        }
    }
}
